#!/usr/bin/env python3
"""
YouTube Channel Video Scraper

This script uses the YouTube Data API v3 to fetch all videos from a specified
YouTube channel along with their view counts and other metadata.
"""

import os
import json
import csv
from datetime import datetime
from typing import List, Dict, Optional
import argparse

try:
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
except ImportError:
    print("Error: Google API client library not installed.")
    print("Please install it with: pip install google-api-python-client")
    exit(1)


class YouTubeScraper:
    def __init__(self, api_key: str):
        """Initialize the YouTube scraper with API key."""
        self.api_key = api_key
        self.youtube = build('youtube', 'v3', developerKey=api_key)
    
    def get_channel_id(self, channel_input: str) -> Optional[str]:
        """
        Get channel ID from either channel URL, username, or channel ID.
        
        Args:
            channel_input: Can be a channel URL, username, or channel ID
            
        Returns:
            Channel ID if found, None otherwise
        """
        # If it's already a channel ID (starts with UC)
        if channel_input.startswith('UC') and len(channel_input) == 24:
            return channel_input
        
        # Extract from URL if it's a YouTube URL
        if 'youtube.com' in channel_input:
            if '/channel/' in channel_input:
                return channel_input.split('/channel/')[-1].split('/')[0]
            elif '/c/' in channel_input or '/@' in channel_input:
                # Handle custom URLs - need to search by username
                username = channel_input.split('/')[-1].replace('@', '')
                return self._get_channel_id_by_username(username)
        
        # Assume it's a username
        return self._get_channel_id_by_username(channel_input)
    
    def _get_channel_id_by_username(self, username: str) -> Optional[str]:
        """Get channel ID by searching for username."""
        try:
            # Try forUsername first (for legacy usernames)
            request = self.youtube.channels().list(
                part='id',
                forUsername=username
            )
            response = request.execute()
            
            if response['items']:
                return response['items'][0]['id']
            
            # If not found, try searching
            search_request = self.youtube.search().list(
                part='snippet',
                q=username,
                type='channel',
                maxResults=1
            )
            search_response = search_request.execute()
            
            if search_response['items']:
                return search_response['items'][0]['snippet']['channelId']
                
        except HttpError as e:
            print(f"Error searching for channel: {e}")
        
        return None
    
    def get_channel_info(self, channel_id: str) -> Optional[Dict]:
        """Get basic channel information."""
        try:
            request = self.youtube.channels().list(
                part='snippet,statistics',
                id=channel_id
            )
            response = request.execute()
            
            if response['items']:
                channel = response['items'][0]
                return {
                    'id': channel['id'],
                    'title': channel['snippet']['title'],
                    'description': channel['snippet']['description'][:200] + '...' if len(channel['snippet']['description']) > 200 else channel['snippet']['description'],
                    'subscriber_count': channel['statistics'].get('subscriberCount', 'Hidden'),
                    'video_count': channel['statistics'].get('videoCount', '0'),
                    'view_count': channel['statistics'].get('viewCount', '0')
                }
        except HttpError as e:
            print(f"Error fetching channel info: {e}")
        
        return None
    
    def get_all_videos(self, channel_id: str, max_results: Optional[int] = None) -> List[Dict]:
        """
        Get all videos from a channel with their metadata.
        
        Args:
            channel_id: YouTube channel ID
            max_results: Maximum number of videos to fetch (None for all)
            
        Returns:
            List of video dictionaries with metadata
        """
        videos = []
        
        try:
            # Get uploads playlist ID
            channel_request = self.youtube.channels().list(
                part='contentDetails',
                id=channel_id
            )
            channel_response = channel_request.execute()
            
            if not channel_response['items']:
                print("Channel not found!")
                return videos
            
            uploads_playlist_id = channel_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # Get all videos from uploads playlist
            next_page_token = None
            
            while True:
                playlist_request = self.youtube.playlistItems().list(
                    part='snippet',
                    playlistId=uploads_playlist_id,
                    maxResults=50,  # Maximum allowed by API
                    pageToken=next_page_token
                )
                playlist_response = playlist_request.execute()
                
                # Extract video IDs
                video_ids = [item['snippet']['resourceId']['videoId'] for item in playlist_response['items']]
                
                # Get detailed video information
                videos_request = self.youtube.videos().list(
                    part='snippet,statistics,contentDetails',
                    id=','.join(video_ids)
                )
                videos_response = videos_request.execute()
                
                # Process videos
                for video in videos_response['items']:
                    video_data = {
                        'video_id': video['id'],
                        'title': video['snippet']['title'],
                        'description': video['snippet']['description'][:200] + '...' if len(video['snippet']['description']) > 200 else video['snippet']['description'],
                        'published_at': video['snippet']['publishedAt'],
                        'view_count': int(video['statistics'].get('viewCount', 0)),
                        'like_count': int(video['statistics'].get('likeCount', 0)),
                        'comment_count': int(video['statistics'].get('commentCount', 0)),
                        'duration': video['contentDetails']['duration'],
                        'url': f"https://www.youtube.com/watch?v={video['id']}"
                    }
                    videos.append(video_data)
                
                # Check if we've reached the limit
                if max_results and len(videos) >= max_results:
                    videos = videos[:max_results]
                    break
                
                # Check for next page
                next_page_token = playlist_response.get('nextPageToken')
                if not next_page_token:
                    break
                    
                print(f"Fetched {len(videos)} videos so far...")
        
        except HttpError as e:
            print(f"Error fetching videos: {e}")
        
        return videos
    
    def save_to_csv(self, videos: List[Dict], filename: str):
        """Save videos data to CSV file."""
        if not videos:
            print("No videos to save!")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['video_id', 'title', 'url', 'view_count', 'like_count', 
                         'comment_count', 'published_at', 'duration', 'description']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for video in videos:
                writer.writerow(video)
        
        print(f"Saved {len(videos)} videos to {filename}")
    
    def save_to_json(self, videos: List[Dict], filename: str):
        """Save videos data to JSON file."""
        if not videos:
            print("No videos to save!")
            return
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(videos, jsonfile, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(videos)} videos to {filename}")


def main():
    parser = argparse.ArgumentParser(description='Scrape YouTube channel videos')
    parser.add_argument('channel', help='YouTube channel URL, username, or channel ID')
    parser.add_argument('--api-key', help='YouTube Data API key (or set YOUTUBE_API_KEY env var)')
    parser.add_argument('--max-videos', type=int, help='Maximum number of videos to fetch')
    parser.add_argument('--output', default='videos', help='Output filename (without extension)')
    parser.add_argument('--format', choices=['csv', 'json', 'both'], default='csv', help='Output format')
    
    args = parser.parse_args()
    
    # Get API key
    api_key = args.api_key or os.getenv('YOUTUBE_API_KEY')
    if not api_key:
        print("Error: YouTube API key required!")
        print("Either use --api-key argument or set YOUTUBE_API_KEY environment variable")
        print("Get your API key from: https://console.developers.google.com/")
        return
    
    # Initialize scraper
    scraper = YouTubeScraper(api_key)
    
    # Get channel ID
    print(f"Looking up channel: {args.channel}")
    channel_id = scraper.get_channel_id(args.channel)
    
    if not channel_id:
        print("Error: Could not find channel!")
        return
    
    # Get channel info
    channel_info = scraper.get_channel_info(channel_id)
    if channel_info:
        print(f"\nChannel: {channel_info['title']}")
        print(f"Subscribers: {channel_info['subscriber_count']}")
        print(f"Total videos: {channel_info['video_count']}")
        print(f"Total views: {channel_info['view_count']}")
        print()
    
    # Get videos
    print("Fetching videos...")
    videos = scraper.get_all_videos(channel_id, args.max_videos)
    
    if not videos:
        print("No videos found!")
        return
    
    # Sort by view count (descending)
    videos.sort(key=lambda x: x['view_count'], reverse=True)
    
    # Display top videos
    print(f"\nFound {len(videos)} videos. Top 10 by views:")
    print("-" * 80)
    for i, video in enumerate(videos[:10], 1):
        print(f"{i:2d}. {video['title'][:50]:<50} | {video['view_count']:>10,} views")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if args.format in ['csv', 'both']:
        csv_filename = f"{args.output}_{timestamp}.csv"
        scraper.save_to_csv(videos, csv_filename)
    
    if args.format in ['json', 'both']:
        json_filename = f"{args.output}_{timestamp}.json"
        scraper.save_to_json(videos, json_filename)


if __name__ == "__main__":
    main()
