#!/usr/bin/env python3
"""
Example usage of the YouTube scraper as a Python module.
"""

import os
from youtube_scraper import YouTubeScraper

def main():
    # Get API key from environment variable
    api_key = os.getenv('YOUTUBE_API_KEY')
    if not api_key:
        print("Please set YOUTUBE_API_KEY environment variable")
        return
    
    # Initialize scraper
    scraper = YouTubeScraper(api_key)
    
    # Example channel (replace with any channel you want to scrape)
    channel_input = "https://www.youtube.com/@mkbhd"  # MKBHD channel
    
    print(f"Scraping channel: {channel_input}")
    
    # Get channel ID
    channel_id = scraper.get_channel_id(channel_input)
    if not channel_id:
        print("Could not find channel!")
        return
    
    print(f"Channel ID: {channel_id}")
    
    # Get channel info
    channel_info = scraper.get_channel_info(channel_id)
    if channel_info:
        print(f"Channel: {channel_info['title']}")
        print(f"Subscribers: {channel_info['subscriber_count']}")
        print(f"Total videos: {channel_info['video_count']}")
    
    # Get first 10 videos (for demo purposes)
    print("\nFetching first 10 videos...")
    videos = scraper.get_all_videos(channel_id, max_results=10)
    
    if videos:
        print(f"\nFound {len(videos)} videos:")
        print("-" * 80)
        
        # Sort by view count
        videos.sort(key=lambda x: x['view_count'], reverse=True)
        
        for i, video in enumerate(videos, 1):
            print(f"{i:2d}. {video['title'][:50]:<50} | {video['view_count']:>10,} views")
        
        # Save to files
        scraper.save_to_csv(videos, "example_videos.csv")
        scraper.save_to_json(videos, "example_videos.json")
        
        print(f"\nSaved {len(videos)} videos to example_videos.csv and example_videos.json")
    else:
        print("No videos found!")

if __name__ == "__main__":
    main()
