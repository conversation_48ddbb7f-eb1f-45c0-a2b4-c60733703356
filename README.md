# YouTube Channel Video Scraper

A Python script that uses the YouTube Data API v3 to scrape all videos from a YouTube channel and export them with view counts and other metadata.

## Features

- Fetch all videos from any YouTube channel
- Get detailed video metadata including:
  - Video title and description
  - View count, like count, comment count
  - Publication date and duration
  - Direct video URL
- Support multiple input formats:
  - Channel URLs (e.g., `https://www.youtube.com/@channelname`)
  - Channel IDs (e.g., `UCxxxxxxxxxxxxxxxxxxxxxx`)
  - Legacy usernames
- Export data in CSV or JSON format
- Sort videos by view count
- Display top videos summary

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Get YouTube API Key

1. Go to the [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select an existing one
3. Enable the **YouTube Data API v3**:
   - Go to "APIs & Services" > "Library"
   - Search for "YouTube Data API v3"
   - Click on it and press "Enable"
4. Create credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the generated API key

### 3. Configure API Key

Option 1: Environment Variable (Recommended)
```bash
export YOUTUBE_API_KEY="your_api_key_here"
```

Option 2: Create .env file
```bash
cp .env.example .env
# Edit .env and add your API key
```

Option 3: Pass as command line argument
```bash
python youtube_scraper.py --api-key "your_api_key_here" [channel]
```

## Usage

### Basic Usage

```bash
# Using channel URL
python youtube_scraper.py "https://www.youtube.com/@channelname"

# Using channel ID
python youtube_scraper.py "UCxxxxxxxxxxxxxxxxxxxxxx"

# Using username (legacy)
python youtube_scraper.py "username"
```

### Advanced Options

```bash
# Limit number of videos
python youtube_scraper.py --max-videos 100 "https://www.youtube.com/@channelname"

# Specify output format
python youtube_scraper.py --format json "https://www.youtube.com/@channelname"
python youtube_scraper.py --format both "https://www.youtube.com/@channelname"

# Custom output filename
python youtube_scraper.py --output "my_channel_data" "https://www.youtube.com/@channelname"

# Provide API key directly
python youtube_scraper.py --api-key "your_key" "https://www.youtube.com/@channelname"
```

### Command Line Arguments

- `channel` (required): YouTube channel URL, username, or channel ID
- `--api-key`: YouTube Data API key (optional if set as environment variable)
- `--max-videos`: Maximum number of videos to fetch (optional, fetches all by default)
- `--output`: Output filename without extension (default: "videos")
- `--format`: Output format - csv, json, or both (default: csv)

## Output

The script generates files with timestamps:
- `videos_YYYYMMDD_HHMMSS.csv` - CSV format with video data
- `videos_YYYYMMDD_HHMMSS.json` - JSON format with video data

### CSV Columns

- `video_id`: YouTube video ID
- `title`: Video title
- `url`: Direct YouTube URL
- `view_count`: Number of views
- `like_count`: Number of likes
- `comment_count`: Number of comments
- `published_at`: Publication date (ISO format)
- `duration`: Video duration (ISO 8601 format)
- `description`: Video description (truncated to 200 chars)

## Example Output

```
Looking up channel: https://www.youtube.com/@examplechannel

Channel: Example Channel
Subscribers: 1,234,567
Total videos: 150
Total views: 50,000,000

Fetching videos...
Fetched 50 videos so far...
Fetched 100 videos so far...
Fetched 150 videos so far...

Found 150 videos. Top 10 by views:
--------------------------------------------------------------------------------
 1. Most Popular Video Title                       |  5,000,000 views
 2. Second Most Popular Video                      |  3,500,000 views
 3. Third Popular Video Title                      |  2,800,000 views
 ...

Saved 150 videos to videos_20231201_143022.csv
```

## API Limits

The YouTube Data API v3 has quotas:
- Default quota: 10,000 units per day
- Each video fetch costs ~3-5 units
- Large channels may require multiple days or quota increases

## Error Handling

The script handles common errors:
- Invalid channel URLs or IDs
- API quota exceeded
- Network connectivity issues
- Private or deleted videos

## Requirements

- Python 3.6+
- Valid YouTube Data API v3 key
- Internet connection

## License

This project is open source. Use responsibly and respect YouTube's Terms of Service.
